---
title: "Homepage Content"
lastUpdated: 2025-01-26
hero:
  headline: "Build Systems That Scale.<br>Lead Teams That Deliver."
  subheadline: "Software architect building enterprise systems that serve thousands of users."
  description: "I help companies build robust backend systems and platforms that scale from startup to enterprise."
  highlights:
    - icon: "🏗️"
      label: "Enterprise Architect"
    - icon: "⚡"
      label: "Performance Expert"
    - icon: "🚀"
      label: "Team Leader"
  primaryCTA:
    text: "View My Work"
    url: "#portfolio"
  secondaryCTA:
    text: "Let's Build Together"
    url: "#contact"
about:
  openingLine: "What started as curiosity about building scalable systems has evolved into architecting enterprise solutions that power modern businesses."
  mainContent:
    - "As a software developer and system architect, I solve complex scalability challenges for companies ranging from startups to enterprises. My approach combines technical expertise with practical business sense, focusing on building systems that scale, perform, and enable business growth."
    - "I've architected core backend systems and e-commerce platforms handling thousands of concurrent users. Each project strengthens my belief that great software is built with technical excellence, clear communication, and deep understanding of business objectives."
  experienceHighlights:
    - "Designed scalable backend systems and microservices"
    - "Built high-performance APIs serving thousands of requests"
    - "Implemented DevOps practices and cloud infrastructure"
    - "Led technical teams and mentored developers"
  skills:
    - category: "Backend Development"
      items:
        - "Java & Spring Framework"
        - "Node.js & Express"
        - "Python & FastAPI"
        - "RESTful APIs & GraphQL"
    - category: "System Architecture"
      items:
        - "Microservices Architecture"
        - "Event-Driven Systems"
        - "Database Design"
        - "Caching Strategies"
    - category: "DevOps & Infrastructure"
      items:
        - "Docker & Kubernetes"
        - "AWS & Cloud Services"
        - "CI/CD Pipelines"
        - "Monitoring & Logging"
    - category: "Development Practices"
      items:
        - "Test-Driven Development"
        - "Code Review & Quality"
        - "Agile Methodologies"
        - "Technical Documentation"
contact:
  social:
    linkedin: "https://linkedin.com/in/nobhokleng"
    github: "https://github.com/Nobhokleng"
  responseTime: "I typically respond to messages within 24-48 hours during business days."
  introText: "Feel free to reach out if you have any questions or opportunities. I'm always interested in discussing new projects, technical challenges, or collaboration opportunities."
---

## Content Management Strategy

This file contains the main homepage copy and messaging for the personal brand website. The content is structured to:

### Hero Section
- **Headline**: Power statement combining technical excellence and leadership
- **Subheadline**: Specific credentials and experience level
- **Description**: Value proposition and scope of expertise
- **Highlights**: Three key differentiators with visual icons
- **CTAs**: Clear action paths for visitors

### About Section
- **Opening Line**: Hook that transitions from curiosity to proven results
- **Main Content**: Two focused paragraphs covering technical expertise and philosophy

### Content Guidelines
- Keep technical details accessible to non-technical decision makers
- Emphasize business impact alongside technical achievements
- Balance confidence with humility
- Use specific numbers and timeframes where possible
- Maintain consistency with portfolio project narratives

### Update History
- 2025-01-26: Initial homepage copy development with enterprise focus
